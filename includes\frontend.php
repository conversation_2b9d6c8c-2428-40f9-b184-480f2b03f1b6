<?php
/**
 * Frontend Interface - Clean AI Image Tool
 */

defined('ABSPATH') || exit;

require_once AI_STYLED_PATH . 'includes/processor.php';
$processor = new AI_Image_Processor();
$overlays = $processor->get_overlays();
$identity = AIStyledImagePlugin::instance()->get_identity_settings();

// Check if this is logging mode
$is_logging_mode = isset($atts['mode']) && $atts['mode'] === 'log';
?>

<div class="ai-image-tool" data-style="<?php echo esc_attr($atts['style']); ?>" data-mode="<?php echo esc_attr($atts['mode'] ?? 'normal'); ?>">
    <div class="ai-tool-container">
        
        <header class="ai-tool-header">
            <h1 class="ai-tool-title"><?php echo esc_html($identity['brand_name'] ?? 'AI STYLED'); ?> IMAGE STUDIO</h1>
            <p class="ai-tool-subtitle">Transform your photos with AI-powered architectural elements using intelligent prompt generation</p>
        </header>

        <form id="ai-form" class="ai-main-form" enctype="multipart/form-data">
            <div class="ai-form-sections">
                
                <div class="ai-upload-section">
                    <h3><?php esc_html_e('Upload Your Photo', 'ai-styled-image'); ?></h3>
                    <div class="ai-upload-area" id="upload-zone">
                        <div class="ai-upload-content">
                            <div class="ai-upload-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </div>
                            <p class="ai-upload-text"><?php esc_html_e('Drop your image here or click to browse', 'ai-styled-image'); ?></p>
                            <span class="ai-upload-specs"><?php esc_html_e('JPG, PNG, WebP • Max 10MB', 'ai-styled-image'); ?></span>
                        </div>
                        
                        <div class="ai-upload-preview" id="image-preview" style="display: none;">
                            <img id="preview-image" src="" alt="Preview">
                            <div class="ai-preview-details">
                                <span id="image-name" class="ai-image-name"></span>
                                <button type="button" class="ai-change-btn"><?php esc_html_e('Change Photo', 'ai-styled-image'); ?></button>
                            </div>
                        </div>
                        
                        <input type="file" id="user-image" name="user_image" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="ai-style-section">
                    <h3><?php esc_html_e('Choose Architecture Style', 'ai-styled-image'); ?></h3>
                    <div class="ai-styles-container">
                        <input type="hidden" id="selected-overlay" name="overlay_id" value="">
                        
                        <?php if (empty($overlays)): ?>
                            <div class="ai-empty-message">
                                <p><?php esc_html_e('No architectural styles available', 'ai-styled-image'); ?></p>
                            </div>
                        <?php else: ?>
                            <div class="ai-styles-grid">
                                <?php foreach ($overlays as $overlay): ?>
                                    <div class="ai-style-card" 
                                         data-id="<?php echo esc_attr($overlay->id); ?>" 
                                         data-category="<?php echo esc_attr($overlay->category); ?>">
                                        <div class="ai-style-preview">
                                            <img src="<?php echo esc_url($overlay->image_url); ?>" 
                                                 alt="<?php echo esc_attr($overlay->title); ?>"
                                                 loading="lazy">
                                        </div>
                                        <div class="ai-style-details">
                                            <h4><?php echo esc_html($overlay->title); ?></h4>
                                            <span class="ai-style-category"><?php echo esc_html(ucfirst(str_replace('_', ' ', $overlay->category))); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="ai-form-section">
                <div class="ai-section-header">
                    <h3><?php esc_html_e('Custom Instructions', 'ai-styled-image'); ?></h3>
                    <p><?php esc_html_e('Describe what you want the AI to do with your images', 'ai-styled-image'); ?></p>
                </div>
                <div class="ai-section-content">
                    <div class="ai-prompt-field">
                        <textarea
                            id="custom-prompt"
                            name="custom_prompt"
                            class="ai-prompt-input"
                            placeholder="<?php esc_attr_e('Merge the element in the middle next to the fountain', 'ai-styled-image'); ?>"
                            rows="3"></textarea>
                        <div class="ai-prompt-help">
                            <small><?php esc_html_e('Optional: When you provide custom instructions, our AI will analyze both your image and the selected style to create an enhanced, optimized prompt that incorporates your specific requirements. Leave blank to use automatic AI-generated instructions.', 'ai-styled-image'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ai-action-section">
                <button type="submit" id="generate-btn" class="ai-generate-btn" disabled>
                    <svg class="ai-btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                    </svg>
                    <?php esc_html_e('Generate AI Image', 'ai-styled-image'); ?>
                </button>
                <div class="ai-status-message" id="status-text"><?php esc_html_e('Select a photo and style to continue', 'ai-styled-image'); ?></div>
            </div>
        </form>

        <div id="processing-section" class="ai-processing-section" style="display: none;">
            <div class="ai-processing-content">
                <div class="ai-processing-header">
                    <div class="ai-processing-animation">
                        <div class="ai-spinner-container">
                            <div class="ai-spinner-outer">
                                <div class="ai-spinner-inner"></div>
                            </div>
                            <div class="ai-processing-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                    <path d="M2 17l10 5 10-5"/>
                                    <path d="M2 12l10 5 10-5"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <h3 id="processing-title"><?php esc_html_e('Preparing your AI image...', 'ai-styled-image'); ?></h3>
                    <p id="processing-description"><?php esc_html_e('Initializing AI processing pipeline', 'ai-styled-image'); ?></p>
                </div>

                <div class="ai-processing-stages">
                    <div class="ai-stage" id="stage-upload">
                        <div class="ai-stage-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="17,8 12,3 7,8"/>
                                <line x1="12" y1="3" x2="12" y2="15"/>
                            </svg>
                        </div>
                        <div class="ai-stage-content">
                            <h4><?php esc_html_e('Uploading Images', 'ai-styled-image'); ?></h4>
                            <p><?php esc_html_e('Preparing your photo and style selection', 'ai-styled-image'); ?></p>
                        </div>
                        <div class="ai-stage-status"></div>
                    </div>

                    <div class="ai-stage" id="stage-analyze">
                        <div class="ai-stage-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                        </div>
                        <div class="ai-stage-content">
                            <h4><?php esc_html_e('Analyzing Content', 'ai-styled-image'); ?></h4>
                            <p><?php esc_html_e('AI is examining your image and architectural style', 'ai-styled-image'); ?></p>
                        </div>
                        <div class="ai-stage-status"></div>
                    </div>

                    <div class="ai-stage" id="stage-generate">
                        <div class="ai-stage-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                            </svg>
                        </div>
                        <div class="ai-stage-content">
                            <h4><?php esc_html_e('Generating Enhancement', 'ai-styled-image'); ?></h4>
                            <p><?php esc_html_e('Creating your AI-enhanced architectural visualization', 'ai-styled-image'); ?></p>
                        </div>
                        <div class="ai-stage-status"></div>
                    </div>

                    <div class="ai-stage" id="stage-finalize">
                        <div class="ai-stage-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 12l2 2 4-4"/>
                                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                                <path d="M3 12h6m6 0h6"/>
                            </svg>
                        </div>
                        <div class="ai-stage-content">
                            <h4><?php esc_html_e('Finalizing Result', 'ai-styled-image'); ?></h4>
                            <p><?php esc_html_e('Optimizing and preparing your enhanced image', 'ai-styled-image'); ?></p>
                        </div>
                        <div class="ai-stage-status"></div>
                    </div>
                </div>

                <div class="ai-progress-wrapper">
                    <div class="ai-progress-bar">
                        <div class="ai-progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="ai-progress-info">
                        <span class="ai-progress-percentage" id="progress-text">0%</span>
                        <span class="ai-progress-time" id="progress-time"><?php esc_html_e('Estimated: 30-60 seconds', 'ai-styled-image'); ?></span>
                    </div>
                </div>

                <button type="button" id="cancel-processing" class="ai-cancel-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                    <?php esc_html_e('Cancel Processing', 'ai-styled-image'); ?>
                </button>
            </div>
        </div>

        <div id="results-section" class="ai-results-section" style="display: none;">
            <div class="ai-results-header">
                <h3><?php esc_html_e('Your AI-Generated Image', 'ai-styled-image'); ?></h3>
                <button type="button" id="close-results" class="ai-close-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            
            <div class="ai-results-content">
                <div class="ai-result-display">
                    <img id="result-image" src="" alt="<?php esc_attr_e('AI Generated Result', 'ai-styled-image'); ?>">
                </div>
                
                <div class="ai-result-actions">
                    <button type="button" id="download-result" class="ai-action-btn ai-btn-primary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        <?php esc_html_e('Download Image', 'ai-styled-image'); ?>
                    </button>

                    <button type="button" id="create-another" class="ai-action-btn ai-btn-secondary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 5v14M5 12h14"/>
                        </svg>
                        <?php esc_html_e('Create Another', 'ai-styled-image'); ?>
                    </button>
                </div>
            </div>
        </div>

        <?php if ($is_logging_mode): ?>
        <!-- Terminal-Style Logging Display Section -->
        <div class="ai-terminal-section" id="ai-terminal-section" style="display: none;">
            <div class="ai-terminal-container">
                <div class="ai-terminal-header">
                    <div class="ai-terminal-controls">
                        <span class="ai-terminal-dot ai-terminal-red"></span>
                        <span class="ai-terminal-dot ai-terminal-yellow"></span>
                        <span class="ai-terminal-dot ai-terminal-green"></span>
                    </div>
                    <div class="ai-terminal-title">AI Image Processing Terminal</div>
                    <div class="ai-terminal-actions">
                        <button class="ai-terminal-clear" id="clear-terminal" title="Clear Terminal">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="ai-terminal-body" id="terminal-output">
                    <div class="ai-terminal-line ai-terminal-system">
                        <span class="ai-terminal-prompt">system@ai-styled-image:~$</span>
                        <span class="ai-terminal-text">Initializing AI Image Processing Pipeline...</span>
                    </div>
                    <div class="ai-terminal-line ai-terminal-info">
                        <span class="ai-terminal-prompt">info</span>
                        <span class="ai-terminal-text">Processing Mode: AI-Generated Prompts (OpenRouter + Replicate)</span>
                    </div>
                    <div class="ai-terminal-line ai-terminal-info">
                        <span class="ai-terminal-prompt">info</span>
                        <span class="ai-terminal-text">OpenRouter Model: <?php echo esc_html(get_option('ai_styled_openrouter_model', 'openai/gpt-4o-mini')); ?></span>
                    </div>
                    <div class="ai-terminal-line ai-terminal-info">
                        <span class="ai-terminal-prompt">info</span>
                        <span class="ai-terminal-text">Replicate Model: <?php echo esc_html(get_option('ai_styled_model_endpoint', 'flux-kontext-apps/multi-image-kontext-max')); ?></span>
                    </div>
                    <div class="ai-terminal-line ai-terminal-ready">
                        <span class="ai-terminal-prompt">ready</span>
                        <span class="ai-terminal-text">System ready. Waiting for image processing request...</span>
                    </div>
                </div>

                <div class="ai-terminal-footer">
                    <div class="ai-terminal-status">
                        <span class="ai-terminal-status-indicator" id="terminal-status">●</span>
                        <span class="ai-terminal-status-text" id="terminal-status-text">Ready</span>
                    </div>
                    <div class="ai-terminal-timestamp" id="terminal-timestamp">
                        <?php echo date('Y-m-d H:i:s'); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>