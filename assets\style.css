/* AI Styled Image - Modern Frontend Styles */

:root {
  --ai-primary: #0f172a;
  --ai-secondary: #3b82f6;
  --ai-accent: #10b981;
  --ai-background: #f8fafc;
  --ai-surface: #ffffff;
  --ai-text: #1e293b;
  --ai-muted: #64748b;
  --ai-border: #e2e8f0;
  --ai-radius: 8px;
  --ai-font: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --ai-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --ai-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ai-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-image-tool {
  font-family: var(--ai-font);
  color: var(--ai-text);
  background: var(--ai-background);
  min-height: 100vh;
  padding: 2rem 1rem;
  line-height: 1.6;
}

.ai-image-tool *,
.ai-image-tool *::before,
.ai-image-tool *::after {
  box-sizing: border-box;
}

.ai-tool-container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--ai-surface);
  border-radius: var(--ai-radius);
  box-shadow: var(--ai-shadow-lg);
  overflow: hidden;
}

.ai-tool-header {
  background: var(--ai-primary);
  color: var(--ai-surface);
  padding: 3rem 2rem;
  text-align: center;
}

.ai-tool-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.ai-tool-subtitle {
  font-size: 1.125rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.ai-main-form {
  padding: 2rem;
}

.ai-form-sections {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.ai-upload-section h3,
.ai-style-section h3,
.ai-form-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--ai-text);
}

.ai-upload-area {
  border: 2px dashed var(--ai-border);
  border-radius: var(--ai-radius);
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: var(--ai-transition);
  background: var(--ai-background);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-upload-area:hover,
.ai-upload-area.drag-over {
  border-color: var(--ai-secondary);
  background: color-mix(in srgb, var(--ai-secondary) 5%, var(--ai-surface));
}

.ai-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.ai-upload-icon {
  width: 48px;
  height: 48px;
  color: var(--ai-secondary);
}

.ai-upload-text {
  margin: 0;
  font-weight: 500;
  color: var(--ai-text);
  font-size: 1rem;
}

.ai-upload-specs {
  font-size: 0.875rem;
  color: var(--ai-muted);
  background: var(--ai-surface);
  padding: 0.5rem 1rem;
  border-radius: calc(var(--ai-radius) / 2);
  border: 1px solid var(--ai-border);
}

.ai-upload-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--ai-surface);
  border-radius: var(--ai-radius);
  border: 2px solid var(--ai-border);
}

.ai-upload-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: calc(var(--ai-radius) / 2);
  border: 2px solid var(--ai-surface);
  box-shadow: var(--ai-shadow);
}

.ai-preview-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ai-image-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-text);
}

.ai-change-btn {
  background: var(--ai-background);
  color: var(--ai-text);
  border: 1px solid var(--ai-border);
  padding: 0.5rem 1rem;
  border-radius: calc(var(--ai-radius) / 2);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--ai-transition);
  align-self: flex-start;
  font-weight: 500;
}

.ai-change-btn:hover {
  background: var(--ai-border);
}

.ai-styles-container {
  width: 100%;
}

.ai-styles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.ai-style-card {
  background: var(--ai-surface);
  border: 2px solid var(--ai-border);
  border-radius: var(--ai-radius);
  overflow: hidden;
  cursor: pointer;
  transition: var(--ai-transition);
  display: flex;
  flex-direction: column;
}

.ai-style-card:hover {
  border-color: var(--ai-secondary);
  box-shadow: var(--ai-shadow);
  transform: translateY(-2px);
}

.ai-style-card.selected {
  border-color: var(--ai-secondary);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--ai-secondary) 20%, transparent);
}

.ai-style-preview {
  flex: 1;
  overflow: hidden;
  background: var(--ai-background);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.ai-style-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ai-style-card:hover .ai-style-preview img {
  transform: scale(1.05);
}

.ai-style-details {
  padding: 1rem;
  background: var(--ai-surface);
  border-top: 1px solid var(--ai-border);
  text-align: center;
}

.ai-style-details h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--ai-text);
}

.ai-style-category {
  font-size: 0.75rem;
  color: var(--ai-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.ai-empty-message {
  text-align: center;
  padding: 2rem;
  color: var(--ai-muted);
}

/* Custom Prompt Field Styles */
.ai-prompt-field {
  margin-top: 1rem;
}

.ai-prompt-input {
  width: 100%;
  min-height: 80px;
  padding: 1rem;
  border: 2px solid var(--ai-border);
  border-radius: var(--ai-radius);
  background: var(--ai-surface);
  color: var(--ai-text);
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
}

.ai-prompt-input:focus {
  outline: none;
  border-color: var(--ai-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-prompt-input::placeholder {
  color: var(--ai-muted);
  font-style: italic;
}

.ai-prompt-help {
  margin-top: 0.5rem;
}

.ai-prompt-help small {
  color: var(--ai-muted);
  font-size: 0.85rem;
  line-height: 1.4;
}

.ai-action-section {
  text-align: center;
  padding: 2rem 0;
  border-top: 1px solid var(--ai-border);
}

.ai-generate-btn {
  background: var(--ai-secondary);
  color: var(--ai-surface);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--ai-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--ai-transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--ai-shadow);
  margin-bottom: 1rem;
}

.ai-generate-btn:hover:not(:disabled) {
  background: color-mix(in srgb, var(--ai-secondary) 90%, black);
  transform: translateY(-1px);
  box-shadow: var(--ai-shadow-lg);
}

.ai-generate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.ai-btn-icon {
  width: 18px;
  height: 18px;
}

.ai-status-message {
  font-size: 0.875rem;
  color: var(--ai-muted);
  font-weight: 500;
}

/* Enhanced Processing Section */
.ai-processing-section {
  border-top: 1px solid var(--ai-border);
  background: linear-gradient(135deg, var(--ai-background) 0%, var(--ai-surface) 100%);
  padding: 3rem 2rem;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-processing-content {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  width: 100%;
}

/* Enhanced Processing Header */
.ai-processing-header {
  margin-bottom: 3rem;
}

.ai-processing-animation {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.ai-spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
}

.ai-spinner-outer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid var(--ai-border);
  border-top: 3px solid var(--ai-secondary);
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.ai-spinner-inner {
  position: absolute;
  top: 10px;
  left: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  border: 2px solid transparent;
  border-bottom: 2px solid var(--ai-accent);
  border-radius: 50%;
  animation: spin-reverse 1.5s linear infinite;
}

.ai-processing-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  color: var(--ai-secondary);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spin-reverse {
  0% { transform: rotate(360deg); }
  100% { transform: rotate(0deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(0.9); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

.ai-processing-header h3 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-text);
  background: linear-gradient(135deg, var(--ai-text) 0%, var(--ai-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-processing-header p {
  color: var(--ai-muted);
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* Processing Stages */
.ai-processing-stages {
  margin: 2rem 0;
  display: grid;
  gap: 1rem;
}

.ai-stage {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: var(--ai-surface);
  border-radius: var(--ai-radius);
  border: 1px solid var(--ai-border);
  transition: all 0.3s ease;
  opacity: 0.5;
}

.ai-stage.active {
  opacity: 1;
  border-color: var(--ai-secondary);
  background: rgba(59, 130, 246, 0.05);
  transform: translateX(4px);
}

.ai-stage.completed {
  opacity: 0.8;
  border-color: var(--ai-accent);
  background: rgba(16, 185, 129, 0.05);
}

.ai-stage-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--ai-border);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.ai-stage.active .ai-stage-icon {
  background: var(--ai-secondary);
  color: white;
  animation: pulse-icon 2s ease-in-out infinite;
}

.ai-stage.completed .ai-stage-icon {
  background: var(--ai-accent);
  color: white;
}

.ai-stage-icon svg {
  width: 20px;
  height: 20px;
}

@keyframes pulse-icon {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.ai-stage-content {
  flex: 1;
  text-align: left;
}

.ai-stage-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--ai-text);
}

.ai-stage-content p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--ai-muted);
  line-height: 1.4;
}

.ai-stage-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--ai-border);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.ai-stage.active .ai-stage-status {
  background: var(--ai-secondary);
  animation: pulse-status 1.5s ease-in-out infinite;
}

.ai-stage.completed .ai-stage-status {
  background: var(--ai-accent);
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Enhanced Progress Bar */
.ai-progress-wrapper {
  margin: 2rem 0;
}

.ai-progress-bar {
  width: 100%;
  height: 12px;
  background: var(--ai-border);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
}

.ai-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--ai-secondary) 0%, var(--ai-accent) 100%);
  border-radius: 6px;
  transition: width 0.5s ease;
  width: 0%;
  position: relative;
  overflow: hidden;
}

.ai-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.ai-progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-progress-percentage {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--ai-text);
}

.ai-progress-time {
  font-size: 0.85rem;
  color: var(--ai-muted);
  font-weight: 500;
}

/* Enhanced Cancel Button */
.ai-cancel-btn {
  background: var(--ai-surface);
  color: var(--ai-text);
  border: 1px solid var(--ai-border);
  padding: 0.875rem 1.75rem;
  border-radius: var(--ai-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.ai-cancel-btn:hover {
  background: var(--ai-background);
  border-color: var(--ai-muted);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ai-cancel-btn svg {
  width: 16px;
  height: 16px;
}

/* Responsive Design for Processing Section */
@media (max-width: 768px) {
  .ai-processing-section {
    padding: 2rem 1rem;
    min-height: 350px;
  }

  .ai-processing-content {
    max-width: 100%;
  }

  .ai-spinner-container {
    width: 60px;
    height: 60px;
  }

  .ai-processing-icon {
    width: 24px;
    height: 24px;
  }

  .ai-processing-header h3 {
    font-size: 1.5rem;
  }

  .ai-stage {
    padding: 0.75rem;
  }

  .ai-stage-icon {
    width: 36px;
    height: 36px;
    margin-right: 0.75rem;
  }

  .ai-stage-icon svg {
    width: 18px;
    height: 18px;
  }

  .ai-stage-content h4 {
    font-size: 0.9rem;
  }

  .ai-stage-content p {
    font-size: 0.8rem;
  }

  .ai-progress-bar {
    height: 10px;
  }

  .ai-progress-info {
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .ai-processing-section {
    padding: 1.5rem 0.75rem;
  }

  .ai-processing-stages {
    gap: 0.75rem;
  }

  .ai-stage {
    padding: 0.5rem;
  }

  .ai-stage-icon {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }

  .ai-cancel-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
  }
}

.ai-results-section {
  border-top: 1px solid var(--ai-border);
  background: var(--ai-background);
  padding: 2rem;
}

.ai-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.ai-results-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--ai-text);
}

.ai-close-btn {
  background: var(--ai-surface);
  color: var(--ai-muted);
  border: 1px solid var(--ai-border);
  width: 32px;
  height: 32px;
  border-radius: var(--ai-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--ai-transition);
}

.ai-close-btn:hover {
  background: var(--ai-background);
  color: var(--ai-text);
}

.ai-close-btn svg {
  width: 16px;
  height: 16px;
}

.ai-results-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  align-items: center;
}

.ai-result-display {
  background: var(--ai-surface);
  border-radius: var(--ai-radius);
  padding: 1rem;
  box-shadow: var(--ai-shadow);
}

.ai-result-display img {
  width: 100%;
  height: auto;
  border-radius: calc(var(--ai-radius) / 2);
  display: block;
}

.ai-result-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ai-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--ai-radius);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--ai-transition);
  border: none;
  text-decoration: none;
  justify-content: center;
}

.ai-action-btn svg {
  width: 16px;
  height: 16px;
}

.ai-btn-primary {
  background: var(--ai-secondary);
  color: var(--ai-surface);
  box-shadow: var(--ai-shadow);
}

.ai-btn-primary:hover {
  background: color-mix(in srgb, var(--ai-secondary) 90%, black);
  transform: translateY(-1px);
  box-shadow: var(--ai-shadow-lg);
}

.ai-btn-secondary {
  background: var(--ai-surface);
  color: var(--ai-text);
  border: 1px solid var(--ai-border);
}

.ai-btn-secondary:hover {
  background: var(--ai-background);
}

@media (max-width: 768px) {
  .ai-image-tool {
    padding: 1rem;
  }
  
  .ai-tool-title {
    font-size: 2rem;
  }
  
  .ai-tool-header {
    padding: 2rem 1rem;
  }
  
  .ai-main-form {
    padding: 1.5rem;
  }
  
  .ai-styles-grid {
    grid-template-columns: 1fr;
  }
  
  .ai-results-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .ai-result-actions {
    flex-direction: row;
  }
  
  .ai-upload-area {
    padding: 1.5rem;
    min-height: 150px;
  }
  
  .ai-upload-icon {
    width: 40px;
    height: 40px;
  }
  
  .ai-generate-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .ai-tool-title {
    font-size: 1.75rem;
  }
  
  .ai-styles-grid {
    grid-template-columns: 1fr;
  }
  
  .ai-style-preview {
    min-height: 100px;
  }
  
  .ai-result-actions {
    flex-direction: column;
  }
  
  .ai-action-btn {
    padding: 0.75rem 1rem;
    font-size: 0.8125rem;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.ai-action-btn:focus,
.ai-generate-btn:focus,
.ai-change-btn:focus,
.ai-cancel-btn:focus,
.ai-close-btn:focus {
  outline: 2px solid var(--ai-secondary);
  outline-offset: 2px;
}

@media (prefers-contrast: high) {
  :root {
    --ai-border: #000000;
    --ai-shadow: 0 0 0 1px #000000;
  }
}

/* Logging Section Styles */
.ai-logging-section {
  margin-top: 2rem;
  background: var(--ai-surface);
  border-radius: var(--ai-radius);
  box-shadow: var(--ai-shadow-lg);
  overflow: hidden;
}

.ai-logging-container {
  padding: 2rem;
}

.ai-logging-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ai-text);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.ai-log-tabs {
  display: flex;
  border-bottom: 2px solid var(--ai-border);
  margin-bottom: 1.5rem;
}

.ai-log-tab {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: var(--ai-muted);
  cursor: pointer;
  transition: var(--ai-transition);
  border-bottom: 3px solid transparent;
}

.ai-log-tab:hover {
  color: var(--ai-text);
  background: var(--ai-background);
}

.ai-log-tab.active {
  color: var(--ai-secondary);
  border-bottom-color: var(--ai-secondary);
}

.ai-log-content {
  position: relative;
}

.ai-log-panel {
  display: none;
}

.ai-log-panel.active {
  display: block;
}

.ai-log-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--ai-border);
}

.ai-log-section-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ai-text);
  margin: 0;
}

.ai-log-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  background: var(--ai-background);
  color: var(--ai-muted);
}

.ai-log-status.success {
  background: #ecfdf5;
  color: #059669;
}

.ai-log-status.processing {
  background: #fef3c7;
  color: #d97706;
}

.ai-log-status.error {
  background: #fef2f2;
  color: #dc2626;
}

.ai-log-item {
  margin-bottom: 1.5rem;
}

.ai-log-item label {
  display: block;
  font-weight: 600;
  color: var(--ai-text);
  margin-bottom: 0.5rem;
}

.ai-log-item span {
  color: var(--ai-muted);
  font-family: var(--ai-font);
}

.ai-log-prompt {
  background: var(--ai-background);
  border: 1px solid var(--ai-border);
  border-radius: var(--ai-radius);
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--ai-text);
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.ai-loading-text {
  color: var(--ai-muted);
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

/* Terminal-Style Logging Section */
.ai-terminal-section {
  margin-top: 2rem;
  background: #1a1a1a;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
}

.ai-terminal-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.ai-terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.ai-terminal-controls {
  display: flex;
  gap: 8px;
}

.ai-terminal-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.ai-terminal-red {
  background: #ff5f56;
}

.ai-terminal-yellow {
  background: #ffbd2e;
}

.ai-terminal-green {
  background: #27ca3f;
}

.ai-terminal-title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.ai-terminal-actions {
  display: flex;
  gap: 8px;
}

.ai-terminal-clear {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.ai-terminal-clear:hover {
  color: #fff;
  background: #404040;
}

.ai-terminal-clear svg {
  width: 16px;
  height: 16px;
}

.ai-terminal-body {
  flex: 1;
  padding: 16px;
  background: #1a1a1a;
  color: #00ff00;
  font-size: 14px;
  line-height: 1.4;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #404040 #1a1a1a;
}

.ai-terminal-body::-webkit-scrollbar {
  width: 8px;
}

.ai-terminal-body::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.ai-terminal-body::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

.ai-terminal-body::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.ai-terminal-line {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  animation: ai-terminal-type 0.5s ease-out;
}

@keyframes ai-terminal-type {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.ai-terminal-prompt {
  color: #00ff00;
  font-weight: bold;
  white-space: nowrap;
  min-width: fit-content;
}

.ai-terminal-text {
  color: #ffffff;
  word-wrap: break-word;
  flex: 1;
}

/* Terminal line types */
.ai-terminal-system .ai-terminal-prompt {
  color: #00ff00;
}

.ai-terminal-info .ai-terminal-prompt {
  color: #00bfff;
}

.ai-terminal-info .ai-terminal-prompt::before {
  content: "[INFO] ";
}

.ai-terminal-success .ai-terminal-prompt {
  color: #00ff00;
}

.ai-terminal-success .ai-terminal-prompt::before {
  content: "[SUCCESS] ";
}

.ai-terminal-error .ai-terminal-prompt {
  color: #ff4444;
}

.ai-terminal-error .ai-terminal-prompt::before {
  content: "[ERROR] ";
}

.ai-terminal-warning .ai-terminal-prompt {
  color: #ffaa00;
}

.ai-terminal-warning .ai-terminal-prompt::before {
  content: "[WARNING] ";
}

.ai-terminal-processing .ai-terminal-prompt {
  color: #ffaa00;
}

.ai-terminal-processing .ai-terminal-prompt::before {
  content: "[PROCESSING] ";
}

.ai-terminal-ready .ai-terminal-prompt {
  color: #00ff00;
}

.ai-terminal-ready .ai-terminal-prompt::before {
  content: "[READY] ";
}

.ai-terminal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #2d2d2d;
  border-top: 1px solid #404040;
  font-size: 12px;
}

.ai-terminal-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
}

.ai-terminal-status-indicator {
  font-size: 8px;
  color: #00ff00;
}

.ai-terminal-status-indicator.processing {
  color: #ffaa00;
  animation: ai-terminal-pulse 1s infinite;
}

.ai-terminal-status-indicator.error {
  color: #ff4444;
}

@keyframes ai-terminal-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.ai-terminal-timestamp {
  color: #666;
  font-family: 'Courier New', monospace;
}

/* JSON/Response formatting */
.ai-terminal-json {
  background: #0d1117;
  border: 1px solid #30363d;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #c9d1d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.ai-terminal-json .json-key {
  color: #79c0ff;
}

.ai-terminal-json .json-string {
  color: #a5d6ff;
}

.ai-terminal-json .json-number {
  color: #79c0ff;
}

.ai-terminal-json .json-boolean {
  color: #ff7b72;
}

.ai-terminal-json .json-null {
  color: #8b949e;
}