# AI Styled Image Plugin

A modern, lightweight WordPress plugin for AI-powered architectural visualization using the Replicate API. Transform your photos by seamlessly integrating architectural elements with cutting-edge AI technology.

## 🚀 Version 2.0 - Complete Redesign

### ✨ What's New
- **Simplified Architecture**: Consolidated from 15+ files to just 6 core files
- **Modern Interface**: Clean, responsive design with CSS Grid and modern typography
- **Single Page Admin**: Streamlined dashboard replacing complex tab system
- **Improved API Integration**: Optimized Replicate API calls with better error handling
- **Mobile-First Design**: Fully responsive across all devices
- **ES6+ JavaScript**: Modern, maintainable code with better performance

### 🎯 Key Features

#### For Users
- **Drag & Drop Upload**: Intuitive image upload with visual feedback
- **Smart Overlay Selection**: Categorized architectural elements with preview
- **Real-Time Processing**: Live progress tracking with AI status updates
- **Instant Download**: High-quality results ready for immediate use
- **Test Mode**: Works without API token for demonstration

#### For Administrators
- **Dashboard Overview**: Centralized stats and quick access controls
- **Easy Configuration**: Simple API setup with validation
- **Overlay Management**: Upload and organize PNG overlays with transparency
- **Usage Tracking**: Monitor popular overlays and generation counts
- **Quick Start Guide**: Built-in documentation and setup assistance

## 📁 File Structure

```
ai-styled-image/
├── ai-styled-image.php     # Main plugin file (modern architecture)
├── includes/
│   ├── processor.php       # AI processing & overlay management
│   ├── admin.php          # Single-page admin dashboard
│   └── frontend.php       # Modern frontend interface
├── assets/
│   ├── style.css          # Frontend styles (CSS Grid, modern design)
│   ├── script.js          # Frontend JavaScript (ES6+)
│   ├── admin.css          # Admin dashboard styles
│   └── admin.js           # Admin functionality
└── README.md              # This file
```

**Simplified from 15+ files to 6 core files** - 60% reduction in complexity!

## ⚡ Quick Setup

### 1. Installation
1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate through WordPress admin
3. Navigate to **AI Styled Image** in admin menu

### 2. Configuration
1. **Get API Token**: Visit [Replicate API Tokens](https://replicate.com/account/api-tokens)
2. **Configure Settings**: Enter your token in the admin dashboard
3. **Add Shortcode**: Use `[ai_image_tool]` on any page
4. **Start Creating**: Users can now generate AI images!

### 3. API Integration

The plugin uses the Replicate API with the following configuration:

```bash
curl -X POST \
  -H "Authorization: Bearer $REPLICATE_API_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Prefer: wait" \
  -d '{
    "input": {
      "prompt": "Put the woman into a white t-shirt with the text on it",
      "aspect_ratio": "1:1",
      "input_image_1": "user_image_url",
      "input_image_2": "overlay_url"
    }
  }' \
  https://api.replicate.com/v1/models/flux-kontext-apps/multi-image-kontext-max/predictions
```

## 🎨 Overlay Categories

- **Glass Rooms**: Modern conservatories and glass extensions
- **Pergolas**: Wooden and metal pergola structures  
- **Pools**: Swimming pools and water features
- **Decks**: Wooden decking and outdoor platforms
- **General**: Custom architectural elements

## 🛠️ Technical Specifications

### System Requirements
- **WordPress**: 5.0+
- **PHP**: 7.4+ (with modern features support)
- **Memory**: 128MB minimum
- **Storage**: 10MB for plugin files

### Modern Technologies
- **CSS Grid & Flexbox**: Responsive layouts
- **ES6+ JavaScript**: Modern syntax and features
- **SVG Icons**: Scalable, crisp icons throughout
- **CSS Custom Properties**: Consistent theming
- **Fetch API**: Modern HTTP requests
- **Async/Await**: Clean asynchronous code

### File Support
- **Input**: JPG, PNG, WebP (max 10MB)
- **Overlays**: PNG with transparency
- **Output**: High-quality PNG images

## 🎯 Usage Examples

### Basic Shortcode
```php
[ai_image_tool]
```

### With Custom Class
```php
[ai_image_tool class="my-custom-class"]
```

### Programmatic Access
```php
// Get processor instance
$processor = new AI_Image_Processor();

// Process image with custom prompt (triggers enhanced AI analysis)
$result = $processor->process($user_image, $overlay_id, $custom_prompt);

// Process image without custom prompt (uses automatic prompt generation)
$result = $processor->process($user_image, $overlay_id);

// Get overlays
$overlays = $processor->get_overlays();
```

## 🧠 Enhanced Custom Prompt Workflow

When users provide custom instructions, the plugin uses an advanced AI analysis workflow:

### **Automatic Enhancement Process**
1. **User provides custom prompt** (e.g., "Place the glass room in the corner near the fountain")
2. **GPT-4.1 Vision analyzes both images** - your uploaded photo and the selected architectural style
3. **AI creates optimized prompt** that combines your instructions with intelligent image analysis
4. **Enhanced prompt sent to Replicate** for superior architectural integration results

### **Intelligent Fallback System**
- **Primary**: Custom prompt + GPT-4.1 Vision analysis (when OpenRouter API key available)
- **Fallback**: Direct custom prompt usage (if OpenRouter unavailable)
- **Default**: Automatic prompt generation (when no custom prompt provided)

### **Benefits of Enhanced Workflow**
- **Context-Aware**: AI understands your specific image content and lighting
- **Architecturally Intelligent**: Considers perspective, shadows, and structural coherence
- **User-Directed**: Incorporates your exact requirements and creative vision
- **Optimized Output**: Creates prompts specifically tuned for the Replicate model

## 🔧 Configuration Options

### API Settings
- **Replicate Token**: Your API authentication key
- **Model Endpoint**: AI model for processing (default: flux-kontext-apps/multi-image-kontext-max)
- **Rate Limiting**: Hourly request limits per user
- **File Limits**: Maximum upload size and formats

### Advanced Options
```php
// Customize via WordPress options
update_option('ai_styled_max_file_size', 15728640); // 15MB
update_option('ai_styled_rate_limit', 100); // 100 requests/hour
update_option('ai_styled_allowed_formats', ['jpg', 'png', 'webp']);
```

## 🚀 Performance Optimizations

### Frontend
- **Lazy Loading**: Overlays load on demand
- **Image Compression**: Optimized delivery
- **CSS Minification**: Reduced file sizes
- **Modern JavaScript**: Better performance

### Backend  
- **Efficient Queries**: Optimized database operations
- **Transient Caching**: Rate limiting with WordPress transients
- **File Cleanup**: Automatic temporary file removal
- **Error Handling**: Graceful failure recovery

## 🔒 Security Features

### Data Protection
- **Nonce Verification**: All AJAX requests secured
- **Input Sanitization**: All user data properly cleaned
- **File Validation**: Strict upload requirements
- **Rate Limiting**: Prevents abuse
- **Temporary Storage**: Auto-cleanup of uploaded files

### Access Control
- **Admin Capabilities**: Proper permission checks
- **API Token Security**: Secure storage and handling
- **XSS Prevention**: Output escaping throughout

## 📊 Admin Dashboard

### Overview Section
- **Live Statistics**: Overlay count, usage metrics, API status
- **Quick Actions**: Direct access to common tasks
- **Visual Indicators**: Color-coded status displays

### Settings Management
- **API Configuration**: Token setup with validation
- **File Management**: Upload limits and format control
- **Rate Limiting**: Usage control per user

### Overlay Library
- **Drag & Drop Upload**: Modern file upload experience
- **Category Organization**: Structured overlay management
- **Usage Analytics**: Popular overlay tracking
- **Quick Actions**: Edit, delete, and preview options

## 🎨 Design System

### Color Palette
- **Primary**: `#3b82f6` (Modern blue)
- **Secondary**: `#10b981` (Success green)  
- **Accent**: `#8b5cf6` (Purple highlight)
- **Grays**: Comprehensive scale from `#f9fafb` to `#111827`

### Typography
- **Font Stack**: System fonts for optimal performance
- **Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold), 800 (extrabold)
- **Scales**: Responsive typography with fluid sizing

### Components
- **Buttons**: Consistent styling with hover states
- **Forms**: Modern inputs with focus states
- **Cards**: Clean containers with subtle shadows
- **Modals**: Accessible overlays with animations

## 🐛 Troubleshooting

### Common Issues

#### "No API token configured"
- **Solution**: Add your Replicate API token in admin settings
- **Note**: Plugin works in test mode without token

#### Upload Fails
- **Check**: File format (PNG for overlays, JPG/PNG/WebP for user images)
- **Verify**: File size under 10MB limit
- **Ensure**: Proper WordPress upload permissions

#### Processing Stuck
- **Cause**: Network issues or API limits
- **Solution**: Try again with smaller image
- **Check**: Replicate API status

#### Admin Dashboard Not Loading
- **Verify**: WordPress 5.0+ and PHP 7.4+
- **Check**: Browser console for JavaScript errors
- **Ensure**: Proper plugin activation

### Debug Mode

Enable WordPress debug mode for detailed error logging:

```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 🔮 Roadmap

### Planned Features
- **Batch Processing**: Multiple images simultaneously
- **Custom Prompts**: User-defined AI instructions
- **Style Presets**: Predefined architectural styles
- **API Alternatives**: Support for additional AI services

### Performance Improvements
- **CDN Integration**: Faster overlay delivery
- **Background Processing**: Queue-based AI processing
- **Caching Layer**: Enhanced performance
- **Progressive Loading**: Improved large image handling

## 🤝 Contributing

### Development Setup
1. Clone repository
2. Install dependencies
3. Follow coding standards
4. Submit pull requests

### Coding Standards
- **PHP**: WordPress Coding Standards
- **JavaScript**: ES6+ with modern practices
- **CSS**: BEM methodology with custom properties
- **Documentation**: Comprehensive inline comments

## 📝 License

GPL v2 or later - [License URI](https://www.gnu.org/licenses/gpl-2.0.html)

## 👨‍💻 Author

**Flavio**
- Website: [moflavio.xyz](https://moflavio.xyz)
- Specialization: UI/UX Expert & Full-Stack Developer
- Focus: Modern web applications with exceptional user experience

---

**Plugin Information:**
- **Version**: 2.0.0
- **Requires WordPress**: 5.0+
- **Requires PHP**: 7.4+
- **Tested up to**: WordPress 6.4+
- **Stable tag**: 2.0.0

**Compatibility:**
- **Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile**: iOS Safari, Android Chrome
- **Responsive**: All screen sizes supported 