/**
 * AI Styled Image - Next.js Inspired Modern Admin JavaScript
 */

class AIStyledAdmin {
    constructor() {
        this.currentTab = 'dashboard';
        this.modal = null;
        this.mediaFrame = null;
        this.currentEditId = null;
        this.galleryData = { images: [], total: 0, offset: 0 };
        this.logsData = { logs: [], total: 0, offset: 0 };
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeComponents();
        this.loadDashboardData();
    }

    bindEvents() {
        // Tab navigation
        document.addEventListener('click', (e) => {
            // Handle tab clicks
            if (e.target.matches('.ai-nav-tab') || e.target.closest('.ai-nav-tab')) {
                e.preventDefault();
                const button = e.target.matches('.ai-nav-tab') ? e.target : e.target.closest('.ai-nav-tab');
                const tab = button.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
                return;
            }
        });

        // Modal and overlay events
        document.addEventListener('click', (e) => {
            if (e.target.matches('#add-overlay-btn, .ai-empty-action')) {
                this.openUploadModal();
            } else if (e.target.matches('#media-library-btn')) {
                this.openMediaLibrary();
            } else if (e.target.matches('#close-upload-modal, #close-lightbox')) {
                this.closeModal();
            } else if (e.target.matches('.ai-modal-overlay')) {
                this.closeModal();
            } else if (e.target.matches('#cancel-upload')) {
                this.closeModal();
            } else if (e.target.matches('#change-image')) {
                this.changeImage();
            } else if (e.target.matches('.ai-delete-overlay')) {
                this.deleteOverlay(e.target.dataset.id);
            } else if (e.target.matches('.ai-edit-overlay')) {
                this.editOverlay(e.target.dataset.id);
            } else if (e.target.matches('.ai-toggle-password')) {
                this.togglePasswordVisibility(e.target.dataset.target);
            }
        });

        // Gallery events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.ai-gallery-item, .ai-gallery-image')) {
                const item = e.target.closest('.ai-gallery-item');
                if (item) {
                    this.openLightbox(item.dataset);
                }
            } else if (e.target.matches('.ai-gallery-delete')) {
                e.stopPropagation();
                this.deleteGalleryImage(e.target.dataset.id);
            }
        });

        // Logs events
        document.addEventListener('click', (e) => {
            if (e.target.matches('#download-logs-btn')) {
                this.downloadLogs();
            } else if (e.target.matches('#clear-logs-btn')) {
                this.clearLogs();
            } else if (e.target.matches('#test-openrouter-btn')) {
                this.testOpenRouterConnection();
            } else if (e.target.matches('#debug-admin-btn')) {
                this.debugAdminInterface();
            }
        });

        // Search and filter events
        document.addEventListener('input', (e) => {
            if (e.target.matches('#gallery-search')) {
                this.debounce(() => this.loadGallery(), 300)();
            } else if (e.target.matches('#log-search')) {
                this.debounce(() => this.loadLogs(), 300)();
            }
        });

        document.addEventListener('change', (e) => {
            if (e.target.matches('#gallery-sort')) {
                this.loadGallery();
            } else if (e.target.matches('#log-category-filter')) {
                this.loadLogs();
            }
        });

        // Form submissions
        const uploadForm = document.getElementById('overlay-upload-form');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleUpload();
            });
        }

        this.setupDropzone();
        this.setupPasswordToggles();
    }

    initializeComponents() {
        // Initialize tab system
        this.switchTab('dashboard');
        this.modal = document.getElementById('upload-modal');

        // Add direct event listeners to tab buttons as fallback
        document.querySelectorAll('.ai-nav-tab').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = button.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });
    }

    // Tab Management
    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.ai-nav-tab').forEach(item => {
            item.classList.remove('nav-tab-active');
        });
        const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeTab) {
            activeTab.classList.add('nav-tab-active');
        }

        // Update content
        document.querySelectorAll('.ai-tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        const activePanel = document.getElementById(`${tabName}-tab`);
        if (activePanel) {
            activePanel.classList.add('active');
        }

        this.currentTab = tabName;

        // Load tab-specific data
        switch (tabName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'gallery':
                this.loadGallery();
                break;
            case 'overlays':
                this.loadOverlays();
                break;
            case 'logs':
                this.loadLogs();
                break;
        }
    }

    // Dashboard Methods
    loadDashboardData() {
        this.loadRecentActivity();
    }

    loadRecentActivity() {
        const activityList = document.getElementById('recent-activity-list');
        if (!activityList) return;

        activityList.innerHTML = '<div class="ai-loading">Loading recent activity...</div>';

        this.makeRequest('ai_styled_get_logs', {
            limit: 5,
            order_by: 'created_at',
            order: 'DESC'
        })
        .then(response => {
            if (response.success) {
                this.renderRecentActivity(response.data.logs);
            } else {
                activityList.innerHTML = '<div class="ai-empty-state"><p>No recent activity</p></div>';
            }
        })
        .catch(() => {
            activityList.innerHTML = `
                <div class="ai-empty-state">
                    <p>No activity yet</p>
                    <small>Activity will appear here once you start using the AI image tool.</small>
                </div>
            `;
        });
    }

    renderRecentActivity(logs) {
        const activityList = document.getElementById('recent-activity-list');
        if (!activityList) return;

        if (logs.length === 0) {
            activityList.innerHTML = '<div class="ai-empty-state"><p>No recent activity</p></div>';
            return;
        }

        const html = logs.map(log => {
            const iconClass = this.getLogIconClass(log.event_category);
            const timeAgo = this.timeAgo(log.created_at);
            
            return `
                <div class="ai-activity-item">
                    <div class="ai-activity-icon ${iconClass}">
                        ${this.getLogIcon(log.event_category)}
                    </div>
                    <div class="ai-activity-content">
                        <div class="ai-activity-title">${log.message}</div>
                        <div class="ai-activity-time">${timeAgo}</div>
                    </div>
                </div>
            `;
        }).join('');

        activityList.innerHTML = html;
    }

    // Gallery Methods
    loadGallery() {
        const galleryGrid = document.getElementById('gallery-grid');
        if (!galleryGrid) return;

        galleryGrid.innerHTML = '<div class="ai-loading">Loading gallery...</div>';

        const searchTerm = document.getElementById('gallery-search')?.value || '';
        const sortValue = document.getElementById('gallery-sort')?.value || 'date-desc';
        const [orderBy, order] = sortValue.split('-');

        this.makeRequest('ai_styled_get_gallery', {
            limit: 24,
            offset: this.galleryData.offset,
            search: searchTerm,
            order_by: orderBy,
            order: order.toUpperCase()
        })
        .then(response => {
            if (response.success) {
                this.galleryData = response.data;
                this.renderGallery(response.data.images);
                this.renderGalleryPagination();
            } else {
                galleryGrid.innerHTML = '<div class="ai-empty-state"><p>Failed to load gallery</p></div>';
            }
        })
        .catch(() => {
            galleryGrid.innerHTML = '<div class="ai-empty-state"><p>Failed to load gallery</p></div>';
        });
    }

    renderGallery(images) {
        const galleryGrid = document.getElementById('gallery-grid');
        if (!galleryGrid) return;

        if (images.length === 0) {
            galleryGrid.innerHTML = `
                <div class="ai-empty-state">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5"/>
                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                    </svg>
                    <h3>No images found</h3>
                    <p>No AI-generated images match your search criteria</p>
                </div>
            `;
            return;
        }

        const html = images.map(image => `
            <div class="ai-gallery-item" data-id="${image.id}" data-url="${image.url}" data-filename="${image.filename}">
                <div class="ai-gallery-image">
                    <img src="${image.url}" alt="${image.filename}" loading="lazy">
                    <div class="ai-gallery-actions">
                        <button class="ai-gallery-action ai-gallery-delete" data-id="${image.id}" title="Delete">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="ai-gallery-info">
                    <div class="ai-gallery-title">${image.filename}</div>
                    <div class="ai-gallery-meta">
                        <span>${image.size_formatted}</span>
                        <span>${image.dimensions.formatted}</span>
                    </div>
                </div>
            </div>
        `).join('');

        galleryGrid.innerHTML = html;
    }

    // Logs Methods
    loadLogs() {
        const logsList = document.getElementById('logs-list');
        if (!logsList) return;

        logsList.innerHTML = '<div class="ai-loading">Loading logs...</div>';

        const searchTerm = document.getElementById('log-search')?.value || '';
        const category = document.getElementById('log-category-filter')?.value || '';

        this.makeRequest('ai_styled_get_logs', {
            limit: 50,
            offset: this.logsData.offset,
            search: searchTerm,
            event_category: category
        })
        .then(response => {
            if (response.success) {
                this.logsData = response.data;
                this.renderLogs(response.data.logs);
                this.renderLogsPagination();
            } else {
                const errorMsg = response.data || 'Failed to load logs';
                logsList.innerHTML = `<div class="ai-empty-state"><p>${errorMsg}</p></div>`;
            }
        })
        .catch(error => {
            console.error('Logs loading error:', error);
            logsList.innerHTML = `
                <div class="ai-empty-state">
                    <h3>No logs available yet</h3>
                    <p>Logs will appear here once you start using the AI image tool.</p>
                </div>
            `;
        });
    }

    renderLogs(logs) {
        const logsList = document.getElementById('logs-list');
        if (!logsList) return;

        if (logs.length === 0) {
            logsList.innerHTML = `
                <div class="ai-empty-state">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                        <polyline points="14,2 14,8 20,8"/>
                    </svg>
                    <h3>No logs found</h3>
                    <p>No log entries match your search criteria</p>
                </div>
            `;
            return;
        }

        const html = logs.map(log => {
            const iconClass = this.getLogIconClass(log.event_category);
            const timeAgo = this.timeAgo(log.created_at);

            return `
                <div class="ai-log-item">
                    <div class="ai-log-icon ${iconClass}">
                        ${this.getLogIcon(log.event_category)}
                    </div>
                    <div class="ai-log-content">
                        <div class="ai-log-message">${log.message}</div>
                        <div class="ai-log-meta">
                            <span>${log.event_category}</span>
                            <span>${timeAgo}</span>
                            ${log.user_id ? `<span>User: ${log.user_id}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        logsList.innerHTML = html;
    }

    // Overlay Methods
    loadOverlays() {
        // This will use existing overlay loading logic
        // The overlays are already rendered in PHP, so we just need to handle interactions
    }

    // Modal Methods
    openUploadModal() {
        this.modal = document.getElementById('upload-modal');
        if (this.modal) {
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            this.resetUploadForm();
        }
    }

    closeModal() {
        const modals = document.querySelectorAll('.ai-modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        document.body.style.overflow = '';
        this.resetUploadForm();
    }

    openLightbox(data) {
        const lightbox = document.getElementById('image-lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxTitle = document.getElementById('lightbox-title');
        const lightboxDetails = document.getElementById('lightbox-details');

        if (lightbox && lightboxImage && lightboxTitle && lightboxDetails) {
            lightboxImage.src = data.url;
            lightboxTitle.textContent = data.filename;
            lightboxDetails.textContent = `Generated AI image`;

            lightbox.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    // Gallery Actions
    deleteGalleryImage(imageId) {
        if (!confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
            return;
        }

        this.makeRequest('ai_styled_delete_gallery_image', {
            image_id: imageId
        })
        .then(response => {
            if (response.success) {
                this.showNotice('Image deleted successfully', 'success');
                this.loadGallery(); // Reload gallery
            } else {
                this.showNotice(response.data || 'Failed to delete image', 'error');
            }
        })
        .catch(() => {
            this.showNotice('Failed to delete image', 'error');
        });
    }

    // Log Actions
    downloadLogs() {
        const category = document.getElementById('log-category-filter')?.value || '';

        this.makeRequest('ai_styled_download_logs', {
            event_category: category
        })
        .then(response => {
            if (response.success) {
                this.downloadFile(response.data.csv_content, response.data.filename, 'text/csv');
                this.showNotice('Logs downloaded successfully', 'success');
            } else {
                this.showNotice('Failed to download logs', 'error');
            }
        })
        .catch(() => {
            this.showNotice('Failed to download logs', 'error');
        });
    }

    clearLogs() {
        if (!confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
            return;
        }

        this.makeRequest('ai_styled_clear_logs', {})
        .then(response => {
            if (response.success) {
                this.showNotice(`Cleared ${response.data.deleted_count} log entries`, 'success');
                this.loadLogs(); // Reload logs
            } else {
                this.showNotice('Failed to clear logs', 'error');
            }
        })
        .catch(() => {
            this.showNotice('Failed to clear logs', 'error');
        });
    }

    // OpenRouter Test
    testOpenRouterConnection() {
        const button = document.getElementById('test-openrouter-btn');
        const resultDiv = document.getElementById('openrouter-test-result');

        if (!button || !resultDiv) return;

        // Update button state
        button.disabled = true;
        button.innerHTML = `
            <svg class="ai-spinner" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
            Testing...
        `;

        // Show result div
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="ai-loading">Testing OpenRouter connection...</div>';

        this.makeRequest('ai_styled_test_openrouter', {})
        .then(response => {
            if (response.success) {
                resultDiv.innerHTML = `
                    <div class="notice notice-success">
                        <p><strong>✅ OpenRouter Connection Successful!</strong></p>
                        <p>Model: ${response.data.model}</p>
                        <p>Response: ${response.data.response}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="notice notice-error">
                        <p><strong>❌ OpenRouter Connection Failed</strong></p>
                        <p>${response.data?.message || 'Unknown error occurred'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('OpenRouter test error:', error);
            resultDiv.innerHTML = `
                <div class="notice notice-error">
                    <p><strong>❌ Test Failed</strong></p>
                    <p>Network error or server issue</p>
                </div>
            `;
        })
        .finally(() => {
            // Reset button
            button.disabled = false;
            button.innerHTML = `
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <circle cx="12" cy="12" r="9"/>
                </svg>
                Test OpenRouter Connection
            `;
        });
    }

    // Debug Admin Interface
    debugAdminInterface() {
        this.makeRequest('ai_styled_debug_admin', {})
        .then(response => {
            if (response.success) {
                console.log('Admin Debug Info:', response.data);

                const debugInfo = response.data;
                let message = 'Admin Interface Debug Info:\n\n';
                message += `Tables Exist: Logs=${debugInfo.tables_exist.logs}, Overlays=${debugInfo.tables_exist.overlays}\n`;
                message += `Directories Exist: Results=${debugInfo.directories_exist.results}, Overlays=${debugInfo.directories_exist.overlays}\n`;
                message += `File Counts: Results=${debugInfo.file_counts.results}, Overlays=${debugInfo.file_counts.overlays}\n`;
                message += `Database Counts: Logs=${debugInfo.database_counts.logs}, Overlays=${debugInfo.database_counts.overlays}\n`;
                message += `User Can Manage: ${debugInfo.user_can_manage}\n`;

                alert(message);

                // If tables don't exist, try to reload the page to trigger table creation
                if (!debugInfo.tables_exist.logs || !debugInfo.tables_exist.overlays) {
                    if (confirm('Some database tables are missing. Would you like to reload the page to create them?')) {
                        window.location.reload();
                    }
                }
            } else {
                this.showNotice('Debug failed: ' + (response.data || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Debug error:', error);
            this.showNotice('Debug request failed', 'error');
        });
    }

    // Utility Methods
    setupDropzone() {
        const dropzone = document.getElementById('upload-dropzone');
        const fileInput = document.getElementById('overlay-file');

        if (!dropzone || !fileInput) return;

        dropzone.addEventListener('click', () => {
            fileInput.click();
        });

        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('drag-over');
        });

        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('drag-over');
        });

        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                this.handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0]);
            }
        });
    }

    setupPasswordToggles() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.ai-toggle-password')) {
                const targetId = e.target.dataset.target;
                const input = document.getElementById(targetId);
                if (input) {
                    input.type = input.type === 'password' ? 'text' : 'password';
                }
            }
        });
    }

    handleFileSelect(file) {
        if (!file.type.startsWith('image/')) {
            this.showNotice('Please select a valid image file.', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.showImagePreview(file.name, file.size, e.target.result);
        };
        reader.readAsDataURL(file);
    }

    showImagePreview(name, size, src) {
        const previewArea = document.getElementById('image-preview-area');
        const previewImg = document.getElementById('preview-img');
        const previewName = document.getElementById('preview-name');
        const previewSize = document.getElementById('preview-size');
        const dropzone = document.getElementById('upload-dropzone');

        if (previewArea && previewImg && previewName && previewSize) {
            previewImg.src = src;
            previewName.textContent = name;
            previewSize.textContent = this.formatFileSize(size);

            previewArea.style.display = 'flex';
            dropzone.style.display = 'none';
        }
    }

    resetUploadForm() {
        const form = document.getElementById('overlay-upload-form');
        const previewArea = document.getElementById('image-preview-area');
        const dropzone = document.getElementById('upload-dropzone');

        if (form) form.reset();
        if (previewArea) previewArea.style.display = 'none';
        if (dropzone) dropzone.style.display = 'block';

        this.currentEditId = null;
    }

    // Helper Methods
    getLogIconClass(category) {
        const classes = {
            api: 'ai-log-icon-api',
            user_action: 'ai-log-icon-user',
            system: 'ai-log-icon-system',
            error: 'ai-log-icon-error',
            image_processing: 'ai-log-icon-api'
        };
        return classes[category] || 'ai-log-icon-system';
    }

    getLogIcon(category) {
        const icons = {
            api: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2L2 7l10 5 10-5-10-5z"/><path d="M2 17l10 5 10-5"/><path d="M2 12l10 5 10-5"/></svg>',
            user_action: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>',
            system: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="m12 1 2.09 6.26L22 9l-6.26 2.09L14 19l-2.09-6.26L4 11l6.26-2.09L12 1z"/></svg>',
            error: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/></svg>',
            image_processing: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>'
        };
        return icons[category] || icons.system;
    }

    timeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    makeRequest(action, data = {}) {
        return fetch(ajaxurl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: action,
                nonce: ai_styled_admin.nonce,
                ...data
            })
        })
        .then(response => response.json());
    }

    showNotice(message, type = 'info') {
        // Create notice element
        const notice = document.createElement('div');
        notice.className = `ai-notice ai-notice-${type}`;
        notice.textContent = message;
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        // Set background color based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        notice.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notice);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notice.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notice.parentNode) {
                    notice.parentNode.removeChild(notice);
                }
            }, 300);
        }, 3000);
    }

    // Placeholder methods for existing functionality
    handleUpload() {
        // Existing upload logic
        console.log('Upload functionality to be implemented');
    }

    deleteOverlay(id) {
        // Existing delete overlay logic
        console.log('Delete overlay functionality to be implemented');
    }

    editOverlay(id) {
        // Existing edit overlay logic
        console.log('Edit overlay functionality to be implemented');
    }

    openMediaLibrary() {
        // Existing media library logic
        console.log('Media library functionality to be implemented');
    }

    togglePasswordVisibility(targetId) {
        const input = document.getElementById(targetId);
        if (input) {
            input.type = input.type === 'password' ? 'text' : 'password';
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.aiAdmin = new AIStyledAdmin();
});

// Global function for tab switching (called from HTML)
window.switchTab = (tabName) => {
    if (window.aiAdmin) {
        window.aiAdmin.switchTab(tabName);
    }
};
