# AI Styled Image Plugin - Complete Disclosure

## Plugin Overview
The **AI Styled Image Plugin** is a WordPress plugin that enables users to generate AI-powered architectural visualizations by combining user-uploaded images with architectural overlay elements using advanced AI APIs.

## Core Functionality

### 1. AI Image Processing Pipeline
- **OpenRouter Integration**: Uses vision models (GPT-4.1) to analyze user images and overlays
- **Replicate Integration**: Uses Flux models for final image generation
- **Intelligent Prompt Generation**: Automatically creates optimized prompts based on image analysis
- **Fallback System**: Graceful degradation when APIs are unavailable

### 2. Overlay Management System
- **PNG Upload Support**: Transparent overlay templates for architectural elements
- **Category Organization**: Glass Rooms, Verandas, Shading Systems
- **Usage Analytics**: Track popular overlays and generation counts
- **Media Library Integration**: Import from WordPress media library

### 3. Frontend Interface
- **Drag & Drop Upload**: Modern file upload with visual feedback
- **Real-time Processing**: Live progress tracking with status updates
- **Responsive Design**: Mobile-first approach with CSS Grid
- **Logging Mode**: Terminal-style debugging interface (`[ai_image_tool mode="log"]`)

### 4. Admin Dashboard
- **Statistics Overview**: Live metrics and API status
- **Gallery Management**: View and manage generated images
- **Activity Logs**: Comprehensive logging system for debugging
- **Settings Management**: API configuration and plugin preferences

### 5. Database Structure
```sql
-- Overlay templates storage
wp_ai_overlays (
    id, title, description, image_url, category, 
    prompt_template, usage_count, created_at
)

-- Comprehensive logging system
wp_ai_styled_logs (
    id, event_type, event_category, message, context,
    user_id, ip_address, user_agent, request_data,
    response_data, execution_time, memory_usage, created_at
)
```

## File Structure Analysis

### Core Files (6 files - 60% reduction from original 15+ files)
1. **ai-styled-image.php** (595 lines) - Main plugin file with modern architecture
2. **includes/processor.php** (1585 lines) - AI processing & overlay management
3. **includes/admin.php** (623 lines) - Single-page admin dashboard
4. **includes/frontend.php** (211 lines) - Modern frontend interface
5. **includes/gallery.php** (305 lines) - Gallery management system
6. **includes/logger.php** (316 lines) - Comprehensive logging system

### Assets
- **assets/style.css** - Frontend styles with CSS Grid and modern design
- **assets/script.js** - Frontend JavaScript with ES6+ features
- **assets/admin.css** - Admin dashboard styles
- **assets/admin.js** - Admin functionality

### Removed Files
- **brand.json** - Empty file with no references in codebase (DELETED)

## API Integration Details

### OpenRouter API (Image Analysis)
```javascript
POST https://openrouter.ai/api/v1/chat/completions
Headers: {
    "Authorization": "Bearer sk-or-...",
    "Content-Type": "application/json",
    "HTTP-Referer": "site_url",
    "X-Title": "AI Styled Image Plugin"
}
Body: {
    "model": "openai/gpt-4.1",
    "messages": [
        {
            "role": "system",
            "content": "You are an AI that creates image generation prompts..."
        },
        {
            "role": "user", 
            "content": [
                {"type": "text", "text": "Create a prompt to integrate..."},
                {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
            ]
        }
    ],
    "max_tokens": 800,
    "temperature": 0.7
}
```

### Replicate API (Image Generation)
```javascript
POST https://api.replicate.com/v1/models/flux-kontext-apps/multi-image-kontext-max/predictions
Headers: {
    "Authorization": "Bearer r8_...",
    "Content-Type": "application/json",
    "Prefer": "wait"
}
Body: {
    "input": {
        "prompt": "Generated prompt from OpenRouter...",
        "aspect_ratio": "match_input_image",
        "input_image_1": "overlay_url",
        "input_image_2": "user_image_url", 
        "output_format": "png",
        "safety_tolerance": 2
    }
}
```

## Logging System Enhancements

### Fixed Issues
1. **Full Request Logging**: Now captures complete API request data
2. **Full Response Logging**: Displays entire API response objects
3. **Terminal Display**: Enhanced JSON formatting in log mode
4. **Request Headers**: Includes authentication and configuration details
5. **Execution Timing**: Precise timing for each API call

### Logging Categories
- **api**: API requests and responses
- **user_action**: User interactions and admin actions
- **system**: Plugin events and configuration changes
- **error**: Error conditions and failures
- **image_processing**: Processing pipeline events

### Enhanced Terminal Mode
Use `[ai_image_tool mode="log"]` to enable detailed logging display:
- Real-time processing updates
- Full API request/response data
- JSON-formatted data display
- Color-coded status indicators
- Execution timing information

## Security Features
- **Nonce Verification**: All AJAX requests secured
- **Input Sanitization**: All user data properly cleaned
- **File Validation**: Strict upload requirements
- **Rate Limiting**: Prevents API abuse
- **Capability Checks**: Proper permission validation
- **API Key Protection**: Secure storage and redacted logging

## Performance Optimizations
- **Lazy Loading**: Overlays load on demand
- **Transient Caching**: Rate limiting with WordPress transients
- **File Cleanup**: Automatic temporary file removal
- **Efficient Queries**: Optimized database operations
- **Modern JavaScript**: ES6+ features for better performance

## Usage Examples

### Basic Implementation
```php
[ai_image_tool]
```

### With Logging Mode
```php
[ai_image_tool mode="log"]
```

### Programmatic Access
```php
$processor = new AI_Image_Processor();
$result = $processor->process($user_image, $overlay_id, $custom_prompt);
```

## Configuration Options
```php
// API Settings
update_option('ai_styled_api_token', 'r8_...');
update_option('ai_styled_openrouter_api_key', 'sk-or-...');

// File Limits
update_option('ai_styled_max_file_size', 10485760); // 10MB
update_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']);

// Rate Limiting
update_option('ai_styled_rate_limit', 50); // requests per hour
```

## Recent Changes Made

### 1. Removed Unused Code
- Deleted empty `brand.json` file (no references found in codebase)

### 2. Enhanced Logging System
- Added full request data capture in `AI_Styled_Logger::log_api_request()`
- Enhanced OpenRouter logging with complete request/response data
- Enhanced Replicate logging with complete request/response data
- Added request size and response size tracking

### 3. Fixed Terminal Display
- Updated `updateLoggingData()` in frontend JavaScript
- Added full request data display for both APIs
- Enhanced JSON formatting and display
- Added comprehensive debugging information

### 4. Improved Error Handling
- Better error response logging for both APIs
- Complete request data included in error logs
- Enhanced debugging capabilities

## System Requirements
- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Memory**: 128MB minimum
- **APIs**: Replicate API token, OpenRouter API key (optional)

## File Storage Structure
```
wp-content/uploads/
├── ai-overlays/           # Uploaded overlay templates
├── ai-temp/              # Temporary processing files
└── ai-styled-image/
    └── results/          # Generated AI images
```

The plugin is now fully disclosed with enhanced logging capabilities and cleaned of unused code.
