# AI Styled Image Plugin - Changelog

## Version 3.1.0 - Two-Stage AI Workflow Implementation

### 🚀 Major Features Added

#### Two-Stage AI Processing Workflow
- **Stage 1**: OpenRouter GPT-4.1 Vision analyzes both the user's photo and template image to generate a custom, contextual prompt
- **Stage 2**: Replicate Flux model uses the generated prompt along with both images to create the final merged result

#### New Admin Configuration Options
- **OpenRouter API Key**: Configure your OpenRouter API key for GPT-4.1 vision models
- **GPT Model Selection**: Choose between:
  - `openai/gpt-4.1` (Full model)
  - `openai/gpt-4.1-mini` (Faster, cost-effective)
  - `openai/gpt-4.1-nano` (Lightweight option)
- **Processing Mode Toggle**:
  - **Current Mode**: Uses manually entered prompts from admin panel (original behavior)
  - **New Mode**: Uses AI-generated prompts via OpenRouter analysis

#### Enhanced Storage Organization
- AI-generated images now stored in structured directory: `wp-content/uploads/ai-styled-image/results/`
- Improved file naming with timestamps: `ai-result-YYYY-MM-DD-HH-MM-SS-{unique_id}.png`

### 🔧 Technical Improvements

#### Backend Changes
- **New OpenRouter Integration**: Complete API client for GPT-4.1 vision models
- **Enhanced Image Processing**: Two-stage workflow with intelligent fallback to current mode
- **Improved Error Handling**: Better error messages and graceful degradation
- **Updated Database Schema**: New settings for OpenRouter configuration

#### Frontend Enhancements
- **Enhanced Progress Feedback**: Two-stage progress indication with descriptive messaging
- **Dynamic UI Updates**: Interface adapts based on selected processing mode
- **Improved User Experience**: Better status messages and processing feedback

#### Admin Panel Updates
- **New Configuration Sections**: Dedicated OpenRouter settings panel
- **Enhanced Status Dashboard**: Shows both Replicate and OpenRouter API status
- **Mode Selection Interface**: Easy toggle between current and new processing modes

### 📋 Workflow Details

#### New Mode (AI-Generated Prompts)
1. User uploads photo and selects architectural template
2. Both images sent to OpenRouter GPT-4.1 for analysis
3. GPT-4.1 generates detailed, contextual prompt based on image analysis
4. Generated prompt + both images sent to Replicate for final image generation
5. Result saved in organized directory structure

#### Current Mode (Manual Prompts)
- Maintains original functionality using predefined or admin-configured prompts
- Serves as fallback if OpenRouter integration fails

### 🛠️ Configuration Requirements

#### For New Mode
- OpenRouter API key (get from https://openrouter.ai/keys)
- Replicate API key (existing requirement)

#### For Current Mode
- Only Replicate API key required (existing setup)

### 🔄 Migration Notes
- Existing installations will default to "New Mode" but can be switched to "Current Mode"
- All existing overlays and settings remain compatible
- No database migration required - new settings added with defaults

### 📁 File Structure Changes
```
wp-content/uploads/
├── ai-styled-image/
│   └── results/           # New: Organized storage for AI results
├── ai-overlays/           # Existing: Template images
└── ai-temp/              # Existing: Temporary processing files
```

### 🎯 Benefits
- **More Accurate Results**: AI analyzes actual images for context-aware prompts
- **Better Integration**: Architectural elements blend more naturally with user photos
- **Flexible Operation**: Choose between AI-generated or manual prompts
- **Organized Storage**: Better file management and organization
- **Enhanced UX**: Clear feedback during two-stage processing

### 🔧 Developer Notes
- OpenRouter integration uses base64 image encoding for vision analysis
- Fallback mechanisms ensure reliability if OpenRouter is unavailable
- Modular design allows easy extension for additional AI providers
- Comprehensive error logging for debugging and monitoring
