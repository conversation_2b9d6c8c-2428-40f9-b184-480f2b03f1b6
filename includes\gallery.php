<?php
/**
 * AI Styled Image - Gallery Management
 */

defined('ABSPATH') || exit;

class AI_Styled_Gallery {
    
    /**
     * Get all AI-generated images from results directory
     */
    public static function get_gallery_images(array $args = []): array {
        $defaults = [
            'limit' => 24,
            'offset' => 0,
            'order_by' => 'date',
            'order' => 'DESC',
            'search' => ''
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $upload_dir = wp_upload_dir();
        $results_dir = $upload_dir['basedir'] . '/ai-styled-image/results/';
        $results_url = $upload_dir['baseurl'] . '/ai-styled-image/results/';
        
        if (!is_dir($results_dir)) {
            return [
                'images' => [],
                'total' => 0,
                'has_more' => false
            ];
        }
        
        $images = [];
        $files = glob($results_dir . '*.{jpg,jpeg,png,webp}', GLOB_BRACE);
        
        if (!$files) {
            return [
                'images' => [],
                'total' => 0,
                'has_more' => false
            ];
        }
        
        // Process each file
        foreach ($files as $file_path) {
            $filename = basename($file_path);
            $file_url = $results_url . $filename;
            
            // Skip if search term doesn't match filename
            if (!empty($args['search']) && stripos($filename, $args['search']) === false) {
                continue;
            }
            
            $file_info = [
                'id' => md5($filename), // Use hash as unique ID
                'filename' => $filename,
                'url' => $file_url,
                'path' => $file_path,
                'size' => filesize($file_path),
                'size_formatted' => size_format(filesize($file_path)),
                'created_at' => date('Y-m-d H:i:s', filemtime($file_path)),
                'created_timestamp' => filemtime($file_path),
                'dimensions' => self::get_image_dimensions($file_path),
                'type' => self::get_image_type($file_path)
            ];
            
            // Extract metadata from filename if available
            if (preg_match('/ai-result-(\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2})-([a-f0-9]+)\.(\w+)/', $filename, $matches)) {
                $file_info['generated_date'] = $matches[1];
                $file_info['unique_id'] = $matches[2];
                $file_info['extension'] = $matches[3];
            }
            
            $images[] = $file_info;
        }
        
        // Sort images
        usort($images, function($a, $b) use ($args) {
            switch ($args['order_by']) {
                case 'name':
                    $result = strcmp($a['filename'], $b['filename']);
                    break;
                case 'size':
                    $result = $a['size'] - $b['size'];
                    break;
                case 'date':
                default:
                    $result = $a['created_timestamp'] - $b['created_timestamp'];
                    break;
            }
            
            return $args['order'] === 'ASC' ? $result : -$result;
        });
        
        $total = count($images);
        
        // Apply pagination
        $images = array_slice($images, $args['offset'], $args['limit']);
        
        return [
            'images' => $images,
            'total' => $total,
            'has_more' => ($args['offset'] + $args['limit']) < $total
        ];
    }
    
    /**
     * Delete a gallery image
     */
    public static function delete_image(string $image_id): array {
        $gallery_data = self::get_gallery_images(['limit' => 1000]); // Get all images
        $target_image = null;
        
        foreach ($gallery_data['images'] as $image) {
            if ($image['id'] === $image_id) {
                $target_image = $image;
                break;
            }
        }
        
        if (!$target_image) {
            return [
                'success' => false,
                'message' => 'Image not found'
            ];
        }
        
        // Check if file exists and delete it
        if (file_exists($target_image['path'])) {
            if (wp_delete_file($target_image['path'])) {
                // Log the deletion
                require_once AI_STYLED_PATH . 'includes/logger.php';
                AI_Styled_Logger::log_user_action(
                    'image_deleted',
                    "Gallery image deleted: {$target_image['filename']}",
                    [
                        'filename' => $target_image['filename'],
                        'file_size' => $target_image['size'],
                        'file_path' => $target_image['path']
                    ]
                );
                
                return [
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete image file'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Image file not found'
            ];
        }
    }
    
    /**
     * Get gallery statistics
     */
    public static function get_gallery_stats(): array {
        $upload_dir = wp_upload_dir();
        $results_dir = $upload_dir['basedir'] . '/ai-styled-image/results/';
        
        $stats = [
            'total_images' => 0,
            'total_size' => 0,
            'total_size_formatted' => '0 B',
            'newest_image' => null,
            'oldest_image' => null,
            'average_size' => 0,
            'types' => []
        ];
        
        if (!is_dir($results_dir)) {
            return $stats;
        }
        
        $files = glob($results_dir . '*.{jpg,jpeg,png,webp}', GLOB_BRACE);
        
        if (!$files) {
            return $stats;
        }
        
        $total_size = 0;
        $newest_time = 0;
        $oldest_time = PHP_INT_MAX;
        $types = [];
        
        foreach ($files as $file_path) {
            $file_size = filesize($file_path);
            $file_time = filemtime($file_path);
            $file_type = self::get_image_type($file_path);
            
            $total_size += $file_size;
            
            if ($file_time > $newest_time) {
                $newest_time = $file_time;
                $stats['newest_image'] = basename($file_path);
            }
            
            if ($file_time < $oldest_time) {
                $oldest_time = $file_time;
                $stats['oldest_image'] = basename($file_path);
            }
            
            if (!isset($types[$file_type])) {
                $types[$file_type] = 0;
            }
            $types[$file_type]++;
        }
        
        $stats['total_images'] = count($files);
        $stats['total_size'] = $total_size;
        $stats['total_size_formatted'] = size_format($total_size);
        $stats['average_size'] = $stats['total_images'] > 0 ? round($total_size / $stats['total_images']) : 0;
        $stats['types'] = $types;
        
        return $stats;
    }
    
    /**
     * Get image dimensions
     */
    private static function get_image_dimensions(string $file_path): array {
        $image_info = getimagesize($file_path);
        
        if ($image_info) {
            return [
                'width' => $image_info[0],
                'height' => $image_info[1],
                'formatted' => $image_info[0] . ' × ' . $image_info[1]
            ];
        }
        
        return [
            'width' => 0,
            'height' => 0,
            'formatted' => 'Unknown'
        ];
    }
    
    /**
     * Get image type
     */
    private static function get_image_type(string $file_path): string {
        $image_info = getimagesize($file_path);
        
        if ($image_info) {
            switch ($image_info[2]) {
                case IMAGETYPE_JPEG:
                    return 'JPEG';
                case IMAGETYPE_PNG:
                    return 'PNG';
                case IMAGETYPE_WEBP:
                    return 'WebP';
                default:
                    return 'Unknown';
            }
        }
        
        return 'Unknown';
    }
    
    /**
     * Clean up old images (optional maintenance function)
     */
    public static function cleanup_old_images(int $days_old = 30): int {
        $upload_dir = wp_upload_dir();
        $results_dir = $upload_dir['basedir'] . '/ai-styled-image/results/';
        
        if (!is_dir($results_dir)) {
            return 0;
        }
        
        $files = glob($results_dir . '*.{jpg,jpeg,png,webp}', GLOB_BRACE);
        $cutoff_time = time() - ($days_old * 24 * 60 * 60);
        $deleted_count = 0;
        
        foreach ($files as $file_path) {
            if (filemtime($file_path) < $cutoff_time) {
                if (wp_delete_file($file_path)) {
                    $deleted_count++;
                }
            }
        }
        
        if ($deleted_count > 0) {
            require_once AI_STYLED_PATH . 'includes/logger.php';
            AI_Styled_Logger::log_system_event(
                'cleanup',
                "Cleaned up {$deleted_count} old gallery images (older than {$days_old} days)"
            );
        }
        
        return $deleted_count;
    }
}
